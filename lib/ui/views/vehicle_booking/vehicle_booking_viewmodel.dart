import 'package:flutter/material.dart';
import 'package:stacked/stacked.dart';
import 'package:stacked_services/stacked_services.dart';
import '../../../app/app.locator.dart';
import '../../../models/address_model.dart';
import '../../../models/vehicle_model.dart';
import '../../../services/address_service.dart';
import '../../../services/distance_service.dart';
import '../map_picker/map_picker_view.dart';
import '../../setup_dialog_ui.dart';
import '../../../debug/distance_test.dart';
import '../../../debug/google_maps_test.dart';

class VehicleBookingViewModel extends BaseViewModel {
  final DialogService _dialogService = locator<DialogService>();
  final AddressService _addressService = AddressService();

  // Constructor - test Google Maps integration
  VehicleBookingViewModel() {
    GoogleMapsTestHelper.logImplementationStatus();
    _initializeVehicles();
  }

  // Initialize with default vehicles
  void _initializeVehicles() {
    _availableVehicles = VehicleModel.getDefaultTrucks();
  }

  // Current step in the process (0: Address, 1: Vehicle, 2: Estimate)
  int _currentStep = 0;
  int get currentStep => _currentStep;

  // Pickup and delivery details
  Map<String, dynamic>? _pickupDetails;
  Map<String, dynamic>? _deliveryDetails;
  Map<String, dynamic>? _estimateDetails;
  DistanceResult? _distanceResult;

  // Vehicle selection
  List<VehicleModel> _availableVehicles = [];
  VehicleModel? _selectedVehicle;

  // Estimate step properties
  String? _selectedPickupTime;
  String? _selectedPickupDate;
  String _selectedDeliveryType = 'standard'; // Default to standard

  String? get selectedPickupTime => _selectedPickupTime;
  String get selectedDeliveryType => _selectedDeliveryType;
  String? get selectedPickupDate => _selectedPickupDate;

  // Review step properties
  bool _termsAccepted = true; // Default to true (checked by default)
  bool get termsAccepted => _termsAccepted;

  Map<String, dynamic>? get pickupDetails => _pickupDetails;
  Map<String, dynamic>? get deliveryDetails => _deliveryDetails;
  Map<String, dynamic>? get estimateDetails => _estimateDetails;

  // Vehicle getters
  List<VehicleModel> get availableVehicles => _availableVehicles;
  VehicleModel? get selectedVehicle => _selectedVehicle;
  bool get hasSelectedVehicle => _selectedVehicle != null;

  // Address getters
  bool get hasPickupAddress => _pickupDetails != null;
  bool get hasDeliveryAddress => _deliveryDetails != null;

  String get pickupAddressDisplay {
    if (_pickupDetails == null) return '';
    return _pickupDetails!['title'] ?? _pickupDetails!['fullAddress'] ?? '';
  }

  String get deliveryAddressDisplay {
    if (_deliveryDetails == null) return '';
    return _deliveryDetails!['title'] ?? _deliveryDetails!['fullAddress'] ?? '';
  }

  // Check if user can proceed to next step
  bool get canProceedToNext {
    switch (_currentStep) {
      case 0: // Address step
        return hasPickupAddress && hasDeliveryAddress && _distanceResult != null;
      case 1: // Vehicle step
        return hasSelectedVehicle;
      case 2: // Estimate step (final step)
        // For standard delivery, time and date are required
        if (_selectedDeliveryType == 'standard') {
          return _selectedPickupTime != null && _selectedPickupDate != null && _termsAccepted;
        }
        // For express delivery, only terms acceptance required
        return _termsAccepted;
      default:
        return false;
    }
  }

  // Move to next step
  void nextStep() {
    if (canProceedToNext && _currentStep < 2) {
      _currentStep++;
      debugPrint('Vehicle Booking - Moved to step: $_currentStep');
      rebuildUi();
    }
  }

  // Move to previous step
  void previousStep() {
    if (_currentStep > 0) {
      _currentStep--;
      debugPrint('Vehicle Booking - Moved back to step: $_currentStep');
      rebuildUi();
    }
  }

  // Go to specific step
  void goToStep(int step) {
    if (step >= 0 && step <= 2) {
      _currentStep = step;
      debugPrint('Vehicle Booking - Jumped to step: $_currentStep');
      rebuildUi();
    }
  }

  // Reset all data
  void resetData() {
    _currentStep = 0;
    _pickupDetails = null;
    _deliveryDetails = null;
    _estimateDetails = null;
    _selectedVehicle = null;
    _selectedPickupTime = null;
    _selectedPickupDate = null;
    _selectedDeliveryType = 'standard';
    _termsAccepted = true;
    _initializeVehicles();
    debugPrint('Vehicle booking data reset');
    rebuildUi();
  }

  // Set pickup details
  void setPickupDetails(Map<String, dynamic> details) {
    _pickupDetails = details;
    debugPrint('Vehicle Booking - Pickup details set: ${details['title']}');
    _calculateDistance();
    rebuildUi();
  }

  // Set delivery details
  void setDeliveryDetails(Map<String, dynamic> details) {
    _deliveryDetails = details;
    debugPrint('Vehicle Booking - Delivery details set: ${details['title']}');
    _calculateDistance();
    rebuildUi();
  }

  // Select vehicle
  void selectVehicle(VehicleModel vehicle) {
    // Update all vehicles to unselected
    _availableVehicles = _availableVehicles.map((v) => v.copyWith(isSelected: false)).toList();
    
    // Select the chosen vehicle
    final index = _availableVehicles.indexWhere((v) => v.id == vehicle.id);
    if (index != -1) {
      _availableVehicles[index] = _availableVehicles[index].copyWith(isSelected: true);
      _selectedVehicle = _availableVehicles[index];
      debugPrint('Vehicle Booking - Selected vehicle: ${vehicle.name}');
      rebuildUi();
    }
  }

  // Set delivery type
  void setDeliveryType(String type) {
    _selectedDeliveryType = type;
    // Reset time and date when switching to express
    if (type == 'express') {
      _selectedPickupTime = null;
      _selectedPickupDate = null;
    }
    debugPrint('Vehicle Booking - Selected delivery type: $type');
    rebuildUi();
  }

  // Toggle terms acceptance
  void toggleTermsAcceptance() {
    _termsAccepted = !_termsAccepted;
    debugPrint('Vehicle Booking - Terms accepted: $_termsAccepted');
    rebuildUi();
  }

  // Get step title
  String getStepTitle() {
    switch (_currentStep) {
      case 0:
        return 'Address Details';
      case 1:
        return 'Select Vehicle';
      case 2:
        return 'Schedule & Confirm';
      default:
        return 'Vehicle Booking';
    }
  }

  // Check if step is completed
  bool isStepCompleted(int step) {
    switch (step) {
      case 0:
        return _pickupDetails != null && _deliveryDetails != null;
      case 1:
        return _selectedVehicle != null;
      case 2:
        return _estimateDetails != null;
      default:
        return false;
    }
  }

  // Calculate distance between pickup and delivery
  Future<void> _calculateDistance() async {
    if (_pickupDetails == null || _deliveryDetails == null) return;

    try {
      final pickupLat = _pickupDetails!['latitude'];
      final pickupLng = _pickupDetails!['longitude'];
      final deliveryLat = _deliveryDetails!['latitude'];
      final deliveryLng = _deliveryDetails!['longitude'];

      if (pickupLat != null && pickupLng != null && deliveryLat != null && deliveryLng != null) {
        final distanceService = DistanceService();
        _distanceResult = await distanceService.calculateRoadDistance(
          originLat: pickupLat,
          originLng: pickupLng,
          destinationLat: deliveryLat,
          destinationLng: deliveryLng,
        );
        
        debugPrint('Vehicle Booking - Distance calculated: ${_distanceResult?.distanceText}');
        rebuildUi();
      }
    } catch (e) {
      debugPrint('Vehicle Booking - Error calculating distance: $e');
    }
  }

  // Handle pickup address selection
  Future<void> handlePickupAddressSelection() async {
    debugPrint('Vehicle Booking - Handling pickup address selection');
    await _showAddressSelectionDialog(isPickup: true);
  }

  // Handle delivery address selection
  Future<void> handleDeliveryAddressSelection() async {
    debugPrint('Vehicle Booking - Handling delivery address selection');
    await _showAddressSelectionDialog(isPickup: false);
  }

  // Show address selection dialog
  Future<void> _showAddressSelectionDialog({required bool isPickup}) async {
    try {
      // Check if there are saved addresses
      final savedAddresses = await _addressService.getSavedAddresses();

      if (savedAddresses.isEmpty) {
        // No saved addresses, go directly to map picker
        debugPrint('Vehicle Booking - No saved addresses found, opening map picker');
        await _openMapPicker(isPickup: isPickup);
      } else {
        // Show address selection dialog
        debugPrint('Vehicle Booking - Showing address selection dialog');
        final response = await _dialogService.showCustomDialog(
          variant: DialogType.addressSelection,
          title: 'Select Address',
          description: 'Choose from saved addresses or add new one',
        );

        if (response?.confirmed == true && response?.data != null) {
          final action = response!.data['action'];

          switch (action) {
            case 'select':
              // Address selected from saved addresses
              final addressData = response.data['address'];
              final address = AddressModel.fromJson(addressData);
              _handleAddressSelected(address, isPickup: isPickup);
              break;

            case 'add_new':
              // Add new address
              await _openMapPicker(isPickup: isPickup);
              break;

            case 'edit':
              // Edit existing address
              final addressData = response.data['address'];
              final address = AddressModel.fromJson(addressData);
              await _openMapPicker(isPickup: isPickup, editAddress: address);
              break;
          }
        }
      }
    } catch (e) {
      debugPrint('Vehicle Booking - Error in address selection: $e');
    }
  }

  // Open map picker
  Future<void> _openMapPicker({required bool isPickup, AddressModel? editAddress}) async {
    try {
      debugPrint('Vehicle Booking - Opening map picker for ${isPickup ? 'pickup' : 'delivery'}');
      
      final result = await Navigator.of(StackedService.navigatorKey!.currentContext!).push(
        MaterialPageRoute(
          builder: (context) => MapPickerView(
            editAddress: editAddress,
          ),
        ),
      );

      if (result != null && result is AddressModel) {
        _handleAddressSelected(result, isPickup: isPickup);
      }
    } catch (e) {
      debugPrint('Vehicle Booking - Error opening map picker: $e');
    }
  }

  // Handle address selected
  void _handleAddressSelected(AddressModel address, {required bool isPickup}) {
    final addressDetails = {
      'id': address.id,
      'title': address.title,
      'fullAddress': address.fullAddress,
      'landmark': address.landmark,
      'latitude': address.latitude,
      'longitude': address.longitude,
      'addressType': address.addressType,
      'contactName': address.contactName,
      'contactPhone': address.contactPhone,
    };

    if (isPickup) {
      setPickupDetails(addressDetails);
      debugPrint('Vehicle Booking - Pickup address selected: ${address.title}');
      DistanceTestHelper.testAddressCoordinates(addressDetails);
    } else {
      setDeliveryDetails(addressDetails);
      debugPrint('Vehicle Booking - Delivery address selected: ${address.title}');
      DistanceTestHelper.testAddressCoordinates(addressDetails);
    }

    rebuildUi();
  }

  // Validate time is between 8 AM and 9 PM
  bool _isValidTime(TimeOfDay time) {
    final hour = time.hour;
    return hour >= 8 && hour <= 21; // 8 AM to 9 PM
  }

  // Select pickup time
  Future<void> selectPickupTime() async {
    final TimeOfDay? picked = await showTimePicker(
      context: StackedService.navigatorKey!.currentContext!,
      initialTime: TimeOfDay.now(),
    );

    if (picked != null) {
      if (_isValidTime(picked)) {
        _selectedPickupTime = picked.format(StackedService.navigatorKey!.currentContext!);
        debugPrint('Vehicle Booking - Selected pickup time: $_selectedPickupTime');
        rebuildUi();
      } else {
        // Show error message
        _showTimeErrorDialog();
      }
    }
  }

  // Show time error dialog
  void _showTimeErrorDialog() {
    ScaffoldMessenger.of(StackedService.navigatorKey!.currentContext!).showSnackBar(
      const SnackBar(
        content: Text('Please select a time between 8am and 9pm'),
        backgroundColor: Colors.red,
      ),
    );
  }

  // Select pickup date
  Future<void> selectPickupDate() async {
    final DateTime? picked = await showDatePicker(
      context: StackedService.navigatorKey!.currentContext!,
      initialDate: DateTime.now(),
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 30)),
    );

    if (picked != null) {
      _selectedPickupDate = '${picked.day}/${picked.month}/${picked.year}';
      debugPrint('Vehicle Booking - Selected pickup date: $_selectedPickupDate');
      rebuildUi();
    }
  }

  // Submit order
  Future<void> submitOrder() async {
    if (!_termsAccepted) {
      debugPrint('Vehicle Booking - Cannot submit order: Terms not accepted');
      return;
    }

    debugPrint('Vehicle Booking - Submitting order...');
    debugPrint('Delivery Type: $_selectedDeliveryType');
    debugPrint('Pickup Address: ${_pickupDetails?['fullAddress']}');
    debugPrint('Delivery Address: ${_deliveryDetails?['fullAddress']}');
    debugPrint('Selected Vehicle: ${_selectedVehicle?.name}');

    if (_selectedDeliveryType == 'standard') {
      debugPrint('Scheduled Time: $_selectedPickupTime');
      debugPrint('Scheduled Date: $_selectedPickupDate');
    }

    // TODO: Implement actual order submission logic
    // This would typically involve API calls to submit the order

    // For now, just show a success message
    debugPrint('Vehicle booking order submitted successfully!');
  }
}
