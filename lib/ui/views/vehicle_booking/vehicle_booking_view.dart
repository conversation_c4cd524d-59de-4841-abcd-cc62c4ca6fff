import 'package:flutter/material.dart';
import 'package:stacked/stacked.dart';

import 'vehicle_booking_viewmodel.dart';
import '../../common/app_colors.dart';

class VehicleBookingView extends StackedView<VehicleBookingViewModel> {
  const VehicleBookingView({super.key});

  @override
  Widget builder(
    BuildContext context,
    VehicleBookingViewModel viewModel,
    Widget? child,
  ) {
    return Scaffold(
      backgroundColor: AppColors.pageBackground,
      appBar: AppBar(
        title: Text(
          viewModel.getStepTitle(),
          style: const TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Colors.black87,
          ),
        ),
        backgroundColor: Colors.white,
        elevation: 0,
        iconTheme: const IconThemeData(color: Colors.black87),
        centerTitle: true,
      ),
      body: Column(
        children: [
          // Progress Indicator Section
          Container(
            color: Colors.white,
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
            child: _buildProgressIndicator(viewModel.currentStep),
          ),

          // Main Content
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(24),
              child: _buildStepContent(viewModel),
            ),
          ),
          
          // Bottom navigation buttons
          Container(
            padding: const EdgeInsets.all(20),
            decoration: const BoxDecoration(
              color: Colors.white,
              boxShadow: [
                BoxShadow(
                  color: Colors.black12,
                  blurRadius: 4,
                  offset: Offset(0, -2),
                ),
              ],
            ),
            child: Row(
              children: [
                // Back button
                if (viewModel.currentStep > 0) ...[
                  Expanded(
                    child: OutlinedButton(
                      onPressed: viewModel.previousStep,
                      style: OutlinedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        side: const BorderSide(color: AppColors.borderColor),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: const Text(
                        'Back',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: Colors.black87,
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 16),
                ],
                
                // Next/Submit button
                Expanded(
                  flex: viewModel.currentStep > 0 ? 1 : 2,
                  child: ElevatedButton(
                    onPressed: viewModel.canProceedToNext 
                        ? () {
                            if (viewModel.currentStep == 2) {
                              viewModel.submitOrder();
                            } else {
                              viewModel.nextStep();
                            }
                          }
                        : null,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.buttonBackground,
                      foregroundColor: AppColors.buttonText,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      elevation: 0,
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          viewModel.currentStep == 2 ? 'Submit Order' : 'Next',
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        const SizedBox(width: 8),
                        Icon(
                          viewModel.currentStep == 2 ? Icons.check_circle : Icons.arrow_forward,
                          size: 20,
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStepContent(VehicleBookingViewModel viewModel) {
    switch (viewModel.currentStep) {
      case 0:
        return _buildAddressStep(viewModel);
      case 1:
        return _buildVehicleStep(viewModel);
      case 2:
        return _buildEstimateStep(viewModel);
      default:
        return _buildAddressStep(viewModel);
    }
  }

  Widget _buildProgressIndicator(int currentStep) {
    final steps = [
      {'title': 'Address', 'icon': Icons.location_on},
      {'title': 'Vehicle', 'icon': Icons.local_shipping},
      {'title': 'Estimate', 'icon': Icons.calendar_today_outlined},
    ];

    return Row(
      children: List.generate(steps.length * 2 - 1, (index) {
        if (index.isEven) {
          // This is a step (icon + title)
          final stepIndex = index ~/ 2;
          final isActive = stepIndex == currentStep;
          final isCompleted = stepIndex < currentStep;

          return Expanded(
            child: Column(
              children: [
                // Icon Circle
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: isActive
                        ? AppColors.borderColor // Dark grey black for active step
                        : isCompleted
                            ? AppColors.buttonBackground
                            : AppColors.inactiveStepColor,
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    steps[stepIndex]['icon'] as IconData,
                    color: isActive || isCompleted ? Colors.yellowAccent : Colors.grey[600],
                    size: 20,
                  ),
                ),
                const SizedBox(height: 8),
                // Step Title
                Text(
                  steps[stepIndex]['title'] as String,
                  style: TextStyle(
                    fontSize: 11,
                    fontWeight: isActive ? FontWeight.w600 : FontWeight.w500,
                    color: isActive
                        ? AppColors.borderColor // Dark grey black for active step text
                        : isCompleted
                            ? AppColors.buttonBackground
                            : AppColors.secondaryText,
                  ),
                ),
              ],
            ),
          );
        } else {
          // This is a connecting line between steps
          final stepIndex = index ~/ 2;
          final isCompleted = stepIndex < currentStep;

          return Expanded(
            child: Container(
              height: 2,
              margin: const EdgeInsets.only(bottom: 32), // Align with icon center
              child: CustomPaint(
                painter: DottedLinePainter(
                  color: isCompleted
                      ? AppColors.completedStepColor
                      : AppColors.inactiveStepColor,
                ),
              ),
            ),
          );
        }
      }),
    );
  }

  Widget _buildAddressStep(VehicleBookingViewModel viewModel) {
    return Column(
      children: [
        // Show detailed address view if pickup address is selected
        if (viewModel.hasPickupAddress) ...[
          _buildDetailedAddressView(viewModel),
        ] else ...[
          // Show selection interface if pickup address not selected
          // Pickup Section
          _buildLocationSection(
            icon: Icons.keyboard_arrow_up,
            iconColor: AppColors.pickupIconColor,
            title: 'PICKUP AT',
            buttonText: 'Add pickup details',
            isAddressSelected: false,
            onPressed: () => viewModel.handlePickupAddressSelection(),
          ),

          const SizedBox(height: 24),

          // Delivery Section
          _buildLocationSection(
            icon: Icons.keyboard_arrow_down,
            iconColor: AppColors.deliveryIconColor,
            title: 'DROP AT',
            buttonText: 'Add delivery details',
            isAddressSelected: false,
            onPressed: () => viewModel.handleDeliveryAddressSelection(),
          ),
        ],
      ],
    );
  }

  Widget _buildVehicleStep(VehicleBookingViewModel viewModel) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Vehicle Selection Header
        const Text(
          'Select Vehicle',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 24),

        // Vehicle Grid (2x2)
        GridView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 2,
            crossAxisSpacing: 16,
            mainAxisSpacing: 16,
            childAspectRatio: 0.85,
          ),
          itemCount: viewModel.availableVehicles.length,
          itemBuilder: (context, index) {
            final vehicle = viewModel.availableVehicles[index];
            return _buildVehicleCard(vehicle, viewModel);
          },
        ),
      ],
    );
  }

  Widget _buildEstimateStep(VehicleBookingViewModel viewModel) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Schedule & Confirm Header
        const Text(
          'Schedule & Confirm',
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 32),

        // Select Delivery Type Section
        const Text(
          'Select Delivery Type',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 16),

        // Delivery type selection
        Row(
          children: [
            Expanded(
              child: _buildDeliveryTypeCard(
                title: 'Standard',
                subtitle: '₹40',
                description: '8am-9pm',
                isSelected: viewModel.selectedDeliveryType == 'standard',
                onTap: () => viewModel.setDeliveryType('standard'),
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildDeliveryTypeCard(
                title: 'Express',
                subtitle: '₹80',
                description: 'Immediate',
                isSelected: viewModel.selectedDeliveryType == 'express',
                onTap: () => viewModel.setDeliveryType('express'),
              ),
            ),
          ],
        ),

        // Show schedule section only for standard delivery
        if (viewModel.selectedDeliveryType == 'standard') ...[
          const SizedBox(height: 32),

          // Schedule Your Pickup Section
          const Text(
            'Schedule Your Pickup',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 16),

          // Date and Time selection
          Row(
            children: [
              Expanded(
                child: _buildScheduleCard(
                  title: 'Date',
                  value: viewModel.selectedPickupDate ?? 'Select date',
                  icon: Icons.calendar_today,
                  onTap: () => viewModel.selectPickupDate(),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildScheduleCard(
                  title: 'Time',
                  value: viewModel.selectedPickupTime ?? 'Select time',
                  icon: Icons.access_time,
                  onTap: () => viewModel.selectPickupTime(),
                ),
              ),
            ],
          ),
        ],

        const SizedBox(height: 32),

        // Review Section
        const Text(
          'Review',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 16),

        // Order Type Card
        _buildOrderDetailCard(
          title: 'Order Type',
          content: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Vehicle Booking',
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Colors.black87,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                viewModel.selectedDeliveryType == 'standard' ? 'Standard Delivery' : 'Express Delivery',
                style: TextStyle(
                  fontSize: 14,
                  color: AppColors.borderColor,
                ),
              ),
            ],
          ),
        ),

        const SizedBox(height: 16),

        // Selected Vehicle Card
        if (viewModel.hasSelectedVehicle) ...[
          _buildOrderDetailCard(
            title: 'Selected Vehicle',
            onEdit: () => viewModel.goToStep(1),
            content: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  viewModel.selectedVehicle!.name,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'Capacity: ${viewModel.selectedVehicle!.formattedCapacity}',
                  style: TextStyle(
                    fontSize: 14,
                    color: AppColors.borderColor,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'Rate: ${viewModel.selectedVehicle!.formattedPricePerKm}',
                  style: TextStyle(
                    fontSize: 14,
                    color: AppColors.borderColor,
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 16),
        ],

        // Pickup Address Card
        _buildOrderDetailCard(
          title: 'Pickup Address',
          onEdit: () => viewModel.handlePickupAddressSelection(),
          content: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (viewModel.hasPickupAddress) ...[
                Text(
                  viewModel.pickupDetails?['fullAddress'] ?? viewModel.pickupAddressDisplay,
                  style: const TextStyle(
                    fontSize: 14,
                    color: Colors.black87,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'Mobile: ${viewModel.pickupDetails?['contactPhone'] ?? ''}',
                  style: TextStyle(
                    fontSize: 14,
                    color: AppColors.borderColor,
                  ),
                ),
              ] else ...[
                Text(
                  'No pickup address selected',
                  style: TextStyle(
                    fontSize: 14,
                    color: AppColors.borderColor,
                  ),
                ),
              ],
            ],
          ),
        ),

        const SizedBox(height: 16),

        // Delivery Address Card
        _buildOrderDetailCard(
          title: 'Delivery Address',
          onEdit: () => viewModel.handleDeliveryAddressSelection(),
          content: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (viewModel.hasDeliveryAddress) ...[
                Text(
                  viewModel.deliveryDetails?['fullAddress'] ?? viewModel.deliveryAddressDisplay,
                  style: const TextStyle(
                    fontSize: 14,
                    color: Colors.black87,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'Mobile: ${viewModel.deliveryDetails?['contactPhone'] ?? ''}',
                  style: TextStyle(
                    fontSize: 14,
                    color: AppColors.borderColor,
                  ),
                ),
              ] else ...[
                Text(
                  'No delivery address selected',
                  style: TextStyle(
                    fontSize: 14,
                    color: AppColors.borderColor,
                  ),
                ),
              ],
            ],
          ),
        ),

        const SizedBox(height: 24),

        // Terms and Conditions
        Row(
          children: [
            Checkbox(
              value: viewModel.termsAccepted,
              onChanged: (value) => viewModel.toggleTermsAcceptance(),
              activeColor: AppColors.buttonBackground,
            ),
            Expanded(
              child: Text(
                'I agree to the terms and conditions',
                style: TextStyle(
                  fontSize: 14,
                  color: AppColors.borderColor,
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildLocationSection({
    required IconData icon,
    required Color iconColor,
    required String title,
    required String buttonText,
    required bool isAddressSelected,
    required VoidCallback onPressed,
  }) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: iconColor,
              letterSpacing: 0.5,
            ),
          ),
          const SizedBox(height: 16),
          SizedBox(
            width: double.infinity,
            height: 48,
            child: ElevatedButton(
              onPressed: onPressed,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.buttonBackground,
                foregroundColor: AppColors.buttonText,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                elevation: 0,
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    buttonText,
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(width: 8),
                  const Icon(
                    Icons.add_circle_outline,
                    size: 18,
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailedAddressView(VehicleBookingViewModel viewModel) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Left side - Icons with connecting line
              Column(
                children: [
                  // Pickup icon
                  Container(
                    width: 32,
                    height: 32,
                    decoration: BoxDecoration(
                      color: AppColors.pickupIconColor,
                      shape: BoxShape.circle,
                    ),
                    child: const Icon(
                      Icons.keyboard_arrow_up,
                      color: Colors.white,
                      size: 20,
                    ),
                  ),
                  // Connecting dashed line
                  Container(
                    width: 2,
                    height: 60,
                    margin: const EdgeInsets.symmetric(vertical: 8),
                    child: CustomPaint(
                      painter: DashedLinePainter(color: Colors.grey[400]!),
                    ),
                  ),
                  // Delivery icon
                  Container(
                    width: 32,
                    height: 32,
                    decoration: BoxDecoration(
                      color: AppColors.deliveryIconColor,
                      shape: BoxShape.circle,
                    ),
                    child: const Icon(
                      Icons.keyboard_arrow_down,
                      color: Colors.white,
                      size: 20,
                    ),
                  ),
                ],
              ),
              const SizedBox(width: 16),
              // Right side - Address details
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Pickup address
                    _buildAddressDetails(
                      title: 'Pickup Address',
                      address: viewModel.pickupAddressDisplay,
                      phone: viewModel.pickupDetails?['contactPhone'] ?? '',
                      onEdit: () => viewModel.handlePickupAddressSelection(),
                    ),
                    const SizedBox(height: 32),
                    // Delivery address
                    if (viewModel.hasDeliveryAddress) ...[
                      _buildAddressDetails(
                        title: 'Delivery Address',
                        address: viewModel.deliveryAddressDisplay,
                        phone: viewModel.deliveryDetails?['contactPhone'] ?? '',
                        onEdit: () => viewModel.handleDeliveryAddressSelection(),
                      ),
                    ] else ...[
                      _buildAddDeliveryButton(viewModel),
                    ],
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildVehicleCard(dynamic vehicle, VehicleBookingViewModel viewModel) {
    final isSelected = vehicle.isSelected;

    return GestureDetector(
      onTap: () => viewModel.selectVehicle(vehicle),
      child: Container(
        decoration: BoxDecoration(
          color: isSelected ? const Color(0xFF2C2C2C) : Colors.white,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isSelected ? const Color(0xFF2C2C2C) : AppColors.borderColor.withOpacity(0.3),
            width: 2,
          ),
          boxShadow: [
            BoxShadow(
              color: AppColors.cardShadowColor,
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Vehicle icon/image placeholder
              Container(
                width: double.infinity,
                height: 60,
                decoration: BoxDecoration(
                  color: isSelected
                      ? Colors.white.withOpacity(0.1)
                      : AppColors.borderColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  Icons.local_shipping,
                  size: 32,
                  color: isSelected ? Colors.white : AppColors.borderColor,
                ),
              ),

              const SizedBox(height: 12),

              // Vehicle name
              Text(
                vehicle.name,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: isSelected ? Colors.white : Colors.black87,
                ),
              ),

              const SizedBox(height: 4),

              // Vehicle capacity
              Text(
                vehicle.formattedCapacity,
                style: TextStyle(
                  fontSize: 14,
                  color: isSelected ? Colors.white.withOpacity(0.8) : AppColors.borderColor,
                ),
              ),

              const SizedBox(height: 8),

              // Vehicle price
              Text(
                vehicle.formattedPricePerKm,
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: isSelected ? Colors.white : AppColors.buttonBackground,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAddressDetails({
    required String title,
    required String address,
    required String phone,
    required VoidCallback onEdit,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              title,
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: Colors.black87,
              ),
            ),
            GestureDetector(
              onTap: onEdit,
              child: Text(
                'Edit',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: AppColors.buttonBackground,
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Text(
          address,
          style: const TextStyle(
            fontSize: 14,
            color: Colors.black87,
          ),
        ),
        if (phone.isNotEmpty) ...[
          const SizedBox(height: 4),
          Text(
            'Mobile: $phone',
            style: TextStyle(
              fontSize: 12,
              color: AppColors.borderColor,
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildAddDeliveryButton(VehicleBookingViewModel viewModel) {
    return Container(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: () => viewModel.handleDeliveryAddressSelection(),
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.deliveryIconColor,
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(vertical: 12),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
        child: const Text(
          'Add delivery details',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
    );
  }

  Widget _buildDeliveryTypeCard({
    required String title,
    required String subtitle,
    required String description,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: isSelected ? const Color(0xFF2C2C2C) : Colors.white,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isSelected ? const Color(0xFF2C2C2C) : AppColors.borderColor.withOpacity(0.3),
            width: 2,
          ),
          boxShadow: [
            BoxShadow(
              color: AppColors.cardShadowColor,
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: isSelected ? Colors.white : Colors.black87,
                  ),
                ),
                const Spacer(),
                if (isSelected)
                  const Icon(
                    Icons.check_circle,
                    color: Colors.white,
                    size: 20,
                  ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              subtitle,
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: isSelected ? Colors.white : AppColors.buttonBackground,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              description,
              style: TextStyle(
                fontSize: 12,
                color: isSelected ? Colors.white.withOpacity(0.8) : AppColors.borderColor,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildScheduleCard({
    required String title,
    required String value,
    required IconData icon,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: AppColors.borderColor.withOpacity(0.3)),
          boxShadow: [
            BoxShadow(
              color: AppColors.cardShadowColor,
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  icon,
                  color: AppColors.buttonBackground,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: AppColors.borderColor,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              value,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: Colors.black87,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildOrderDetailCard({
    required String title,
    required Widget content,
    VoidCallback? onEdit,
  }) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.borderColor.withOpacity(0.3)),
        boxShadow: [
          BoxShadow(
            color: AppColors.cardShadowColor,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                title,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Colors.black87,
                ),
              ),
              if (onEdit != null)
                GestureDetector(
                  onTap: onEdit,
                  child: Text(
                    'Edit',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      color: AppColors.buttonBackground,
                    ),
                  ),
                ),
            ],
          ),
          const SizedBox(height: 12),
          content,
        ],
      ),
    );
  }

  @override
  VehicleBookingViewModel viewModelBuilder(BuildContext context) =>
      VehicleBookingViewModel();
}

class DashedLinePainter extends CustomPainter {
  final Color color;

  DashedLinePainter({required this.color});

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..strokeWidth = 2
      ..style = PaintingStyle.stroke;

    const dashHeight = 4;
    const dashSpace = 4;
    double startY = 0;

    while (startY < size.height) {
      canvas.drawLine(
        Offset(size.width / 2, startY),
        Offset(size.width / 2, startY + dashHeight),
        paint,
      );
      startY += dashHeight + dashSpace;
    }
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}

class DottedLinePainter extends CustomPainter {
  final Color color;
  final double dashHeight;
  final double dashSpace;
  final double strokeWidth;

  DottedLinePainter({
    this.color = Colors.grey,
    this.dashHeight = 4,
    this.dashSpace = 4,
    this.strokeWidth = 2,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..strokeWidth = strokeWidth
      ..style = PaintingStyle.stroke;

    double startX = 0;
    final centerY = size.height / 2;

    while (startX < size.width) {
      canvas.drawLine(
        Offset(startX, centerY),
        Offset(startX + dashHeight, centerY),
        paint,
      );
      startX += dashHeight + dashSpace;
    }
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}
